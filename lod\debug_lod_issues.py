#!/usr/bin/env python3
"""
LOD可见性问题调试脚本

用于排查以下问题：
1. 相机离得远时块不显示
2. 相机很近时也不显示LOD Tile
3. 只有进入bounding box才显示
4. 远处显示了低LOD，靠近后反而消失

使用方法：
1. 在Isaac Sim中打开你的场景
2. 运行此脚本
3. 移动相机到不同位置观察输出
"""

import omni.usd
from pxr import Usd, UsdGeom, Gf
import sys
import os

# 添加lod模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lod_scheduler import LODScheduler, TilesetLODManager, BoundingBox
from lod_config import get_default_lod_config

def debug_current_lod_state():
    """调试当前LOD状态"""
    print("=" * 60)
    print("LOD可见性问题调试工具")
    print("=" * 60)
    
    # 获取当前stage
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 错误：无法获取USD Stage")
        return
        
    print("✅ 成功获取USD Stage")
    
    # 创建LOD调度器
    try:
        config = get_default_lod_config()
        scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)
        print("✅ 成功创建LOD调度器")
    except Exception as e:
        print(f"❌ 创建LOD调度器失败: {e}")
        return
        
    # 运行调试分析
    try:
        debug_info = scheduler.debug_lod_visibility_issues(verbose=True)
        
        print("\n" + "=" * 60)
        print("问题诊断")
        print("=" * 60)
        
        # 分析可能的问题
        issues_found = []
        
        # 问题1：相机在边界框内但距离计算为0
        if debug_info.get("camera_inside_bounds") and debug_info.get("sse_distance", 0) < 1.0:
            issues_found.append("🔍 发现问题1: 相机在边界框内，SSE距离过小可能导致计算问题")
            
        # 问题2：距离计算异常
        center_dist = debug_info.get("center_distance", 0)
        sse_dist = debug_info.get("sse_distance", 0)
        if abs(center_dist - sse_dist) > center_dist * 0.5:
            issues_found.append(f"🔍 发现问题2: 中心距离({center_dist:.1f}m)与SSE距离({sse_dist:.1f}m)差异较大")
            
        # 问题3：不在视锥体内
        if not debug_info.get("in_frustum", True):
            issues_found.append("🔍 发现问题3: 场景不在相机视锥体内，可能被错误剔除")
            
        # 问题4：LOD选择边界问题
        selected_lod = debug_info.get("selected_lod")
        distance_ranges = debug_info.get("distance_ranges", {})
        current_distance = debug_info.get("sse_distance", 0)
        
        # 检查是否在LOD切换边界附近
        for lod_name, (min_dist, max_dist) in distance_ranges.items():
            if lod_name == selected_lod:
                # 检查是否在边界的5%范围内
                range_size = max_dist - min_dist if max_dist != float('inf') else min_dist
                boundary_tolerance = range_size * 0.05
                
                if (current_distance - min_dist) < boundary_tolerance:
                    issues_found.append(f"🔍 发现问题4: 距离({current_distance:.1f}m)接近{lod_name} LOD下边界({min_dist:.1f}m)，可能出现切换不稳定")
                elif max_dist != float('inf') and (max_dist - current_distance) < boundary_tolerance:
                    issues_found.append(f"🔍 发现问题4: 距离({current_distance:.1f}m)接近{lod_name} LOD上边界({max_dist:.1f}m)，可能出现切换不稳定")
                    
        if issues_found:
            print("\n🚨 发现以下潜在问题:")
            for i, issue in enumerate(issues_found, 1):
                print(f"{i}. {issue}")
        else:
            print("\n✅ 未发现明显问题，LOD系统配置看起来正常")
            
        print("\n" + "=" * 60)
        print("建议解决方案")
        print("=" * 60)
        
        if debug_info.get("camera_inside_bounds"):
            print("💡 建议1: 相机在边界框内时，尝试移动相机到边界框外测试")
            print("💡 建议2: 检查距离计算逻辑，确保内部距离计算合理")
            
        if not debug_info.get("in_frustum", True):
            print("💡 建议3: 检查视锥体剔除逻辑，可能过于严格")
            print("💡 建议4: 尝试增大FOV或调整near/far平面")
            
        if selected_lod:
            print(f"💡 建议5: 当前应该显示 {selected_lod} LOD，检查对应的Prim是否存在且可见")
            
        print("\n" + "=" * 60)
        print("实时监控命令")
        print("=" * 60)
        print("移动相机后，再次运行以下命令查看变化：")
        print("debug_current_lod_state()")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def debug_tileset_lod_visibility():
    """专门调试update_tileset_lod_visibility方法的问题"""
    print("\n" + "=" * 60)
    print("Tileset LOD Visibility 调试")
    print("=" * 60)

    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 错误：无法获取USD Stage")
        return

    # 创建TilesetLODManager
    try:
        config = get_default_lod_config()
        scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)
        manager = TilesetLODManager(stage, camera_path="/World/Camera", lod_scheduler=scheduler, config=config)
        print("✅ 成功创建TilesetLODManager")
    except Exception as e:
        print(f"❌ 创建TilesetLODManager失败: {e}")
        return

    # 获取相机位置
    camera = stage.GetPrimAtPath("/World/Camera")
    if not camera:
        print("❌ 无法找到相机")
        return

    xformable = UsdGeom.Xformable(camera)
    transform = xformable.GetLocalTransformation()
    camera_position = Gf.Vec3f(transform.ExtractTranslation())
    print(f"📍 相机位置: {camera_position}")

    # 获取区域边界
    region_bounds = manager.get_tileset_region_bounds()
    if not region_bounds:
        print("❌ 无法获取tileset区域边界")
        return

    print(f"📦 区域边界: {region_bounds.min_point} 到 {region_bounds.max_point}")
    print(f"📦 区域中心: {region_bounds.center}")

    # 收集所有tiles
    all_tiles = manager.collect_all_tiles()
    print(f"📋 找到 {len(all_tiles)} 个tiles")

    if not all_tiles:
        print("❌ 没有找到任何tiles")
        return

    # 分析每个tile的LOD选择
    print(f"\n📊 逐个Tile分析:")

    from tileset_utils import FrustumCuller

    # 提取视锥体平面
    frustum_planes, _, _ = FrustumCuller.extract_camera_frustum_planes(camera)
    frustum_available = frustum_planes is not None
    print(f"👁️  视锥体检测: {'可用' if frustum_available else '不可用'}")

    issues_found = []

    for i, tile in enumerate(all_tiles[:5]):  # 只分析前5个tile避免输出过多
        print(f"\n--- Tile {i+1}: {tile['name']} ---")

        tile_bounds = tile.get('bounds')
        if not tile_bounds:
            print("  ⚠️  警告: 该tile没有边界信息")
            issues_found.append(f"Tile {tile['name']} 缺少边界信息")
            continue

        print(f"  边界: {tile_bounds.min_point} 到 {tile_bounds.max_point}")
        print(f"  中心: {tile_bounds.center}")

        # 计算距离
        tile_distance = math.sqrt(
            (camera_position[0] - tile_bounds.center[0])**2 +
            (camera_position[1] - tile_bounds.center[1])**2 +
            (camera_position[2] - tile_bounds.center[2])**2
        )
        print(f"  距离: {tile_distance:.2f}m")

        # LOD选择
        try:
            tile_selected_lod, tile_lod_info = scheduler.select_lod_by_sse_and_distance(
                tile_bounds, camera_position, verbose=False
            )
            print(f"  选择的LOD: {tile_selected_lod}")
        except Exception as e:
            print(f"  ❌ LOD选择失败: {e}")
            issues_found.append(f"Tile {tile['name']} LOD选择失败: {e}")
            continue

        # 视锥体检测
        if frustum_available and tile_bounds:
            frustum_visible = FrustumCuller.is_bounding_box_in_frustum(tile_bounds, frustum_planes)
            print(f"  视锥体内: {'是' if frustum_visible else '否'}")
            if not frustum_visible:
                issues_found.append(f"Tile {tile['name']} 被视锥体剔除")
        else:
            print(f"  视锥体内: 无法检测")

        # 分析content nodes
        content_nodes = tile.get('content_nodes', [])
        print(f"  Content节点数: {len(content_nodes)}")

        if not content_nodes:
            issues_found.append(f"Tile {tile['name']} 没有content节点")
            continue

        # LOD级别映射
        lod_name_to_level = {"High": 20, "Medium": 18, "Low": 16, "VeryLow": 14}
        tile_target_lod_level = lod_name_to_level.get(tile_selected_lod, 14)

        matching_contents = 0
        for content in content_nodes:
            content_lod_level = content.get('lod_level', 14)
            lod_match = (content_lod_level == tile_target_lod_level)
            if lod_match:
                matching_contents += 1
            print(f"    Content LOD {content_lod_level}: {'匹配' if lod_match else '不匹配'}")

        if matching_contents == 0:
            issues_found.append(f"Tile {tile['name']} 没有匹配的content LOD级别 (目标: {tile_target_lod_level})")

    # 总结问题
    print(f"\n" + "=" * 60)
    print("问题总结")
    print("=" * 60)

    if issues_found:
        print("🚨 发现以下问题:")
        for i, issue in enumerate(issues_found, 1):
            print(f"{i}. {issue}")
    else:
        print("✅ 未发现明显问题")

    # 建议
    print(f"\n💡 调试建议:")
    print("1. 运行 update_tileset_lod_visibility(verbose=True) 查看详细输出")
    print("2. 检查LOD级别映射是否正确")
    print("3. 如果视锥体剔除过严，考虑调整相机FOV或禁用视锥体剔除")
    print("4. 检查tile的边界信息是否正确")

def test_distance_calculation():
    """测试距离计算的准确性"""
    print("\n" + "=" * 60)
    print("距离计算测试")
    print("=" * 60)

    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 错误：无法获取USD Stage")
        return

    config = get_default_lod_config()
    scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)

    camera_pos = scheduler.get_camera_position()
    if not camera_pos:
        print("❌ 无法获取相机位置")
        return

    # 创建测试边界框
    test_cases = [
        # 相机在外部的情况
        BoundingBox(Gf.Vec3f(100, 100, 100), Gf.Vec3f(200, 200, 200)),
        # 相机可能在内部的情况
        BoundingBox(Gf.Vec3f(-100, -100, -100), Gf.Vec3f(100, 100, 100)),
        # 小边界框
        BoundingBox(Gf.Vec3f(-1, -1, -1), Gf.Vec3f(1, 1, 1)),
    ]

    print(f"相机位置: {camera_pos}")

    for i, bbox in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}:")
        print(f"  边界框: {bbox.min_point} 到 {bbox.max_point}")
        print(f"  中心: {bbox.center}")
        print(f"  尺寸: {bbox.size}")

        # 检查相机是否在内部
        inside = bbox.contains_point(camera_pos)
        print(f"  相机在内部: {'是' if inside else '否'}")

        # 计算不同的距离
        center_dist = scheduler.calculate_distance_to_camera(bbox, camera_pos)
        sse_dist = scheduler.calculate_distance_to_bounding_sphere(
            camera_pos, bbox.center, bbox.size
        )

        print(f"  中心距离: {center_dist:.2f}m")
        print(f"  SSE距离: {sse_dist:.2f}m")

        # 计算SSE
        for lod_name, geometric_error in scheduler.lod_geometric_errors.items():
            sse = scheduler.calculate_sse(geometric_error, sse_dist)
            print(f"  {lod_name} SSE: {sse:.2f}px")

def debug_lod_transition_deadzone():
    """调试LOD过渡死区问题 - 专门针对18->20切换时的消失问题"""
    print("\n" + "=" * 60)
    print("LOD过渡死区问题调试 (18->20切换)")
    print("=" * 60)

    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 错误：无法获取USD Stage")
        return

    # 创建调度器
    try:
        config = get_default_lod_config()
        scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)
        manager = TilesetLODManager(stage, camera_path="/World/Camera", lod_scheduler=scheduler, config=config)
        print("✅ 成功创建调度器")
    except Exception as e:
        print(f"❌ 创建调度器失败: {e}")
        return

    # 获取相机位置和区域边界
    camera_position = scheduler.get_camera_position()
    region_bounds = manager.get_tileset_region_bounds()

    if not camera_position or not region_bounds:
        print("❌ 无法获取相机位置或区域边界")
        return

    print(f"📍 相机位置: {camera_position}")
    print(f"📦 区域中心: {region_bounds.center}")
    print(f"📦 区域尺寸: {region_bounds.size}")

    # 计算当前距离（两种方法）
    center_distance = math.sqrt(
        (camera_position[0] - region_bounds.center[0])**2 +
        (camera_position[1] - region_bounds.center[1])**2 +
        (camera_position[2] - region_bounds.center[2])**2
    )

    sse_distance = scheduler.calculate_distance_to_bounding_sphere(
        camera_position, region_bounds.center, region_bounds.size
    )

    print(f"📏 中心距离: {center_distance:.2f}m")
    print(f"📏 SSE距离: {sse_distance:.2f}m")
    print(f"📏 距离差异: {abs(center_distance - sse_distance):.2f}m")

    # 获取距离范围和阈值
    distance_ranges, lod_threshold_distances = scheduler.calculate_lod_distance_ranges()

    print(f"\n📊 LOD距离阈值:")
    for lod_name in ["High", "Medium", "Low", "VeryLow"]:
        threshold = lod_threshold_distances.get(lod_name, 0)
        print(f"  {lod_name:8s}: {threshold:6.1f}m")

    print(f"\n📊 LOD距离范围:")
    current_lod = None
    for lod_name in ["High", "Medium", "Low", "VeryLow"]:
        if lod_name in distance_ranges:
            min_dist, max_dist = distance_ranges[lod_name]
            max_dist_str = f"{max_dist:.1f}m" if max_dist != float('inf') else "∞"

            # 检查当前距离是否在范围内
            in_range = min_dist <= sse_distance < max_dist
            status = "✅ 当前" if in_range else "⭕"
            if in_range:
                current_lod = lod_name

            print(f"  {lod_name:8s}: {min_dist:6.1f}m - {max_dist_str:>8s} {status}")

    print(f"\n🎯 当前应该选择的LOD: {current_lod}")

    # 实际LOD选择
    selected_lod, lod_info = scheduler.select_lod_by_sse_and_distance(
        region_bounds, camera_position, verbose=False
    )
    print(f"🎯 实际选择的LOD: {selected_lod}")

    if current_lod != selected_lod:
        print(f"⚠️  警告: 预期LOD与实际LOD不匹配!")

    # 分析关键的Medium->High切换点
    medium_threshold = lod_threshold_distances.get("Medium", 0)
    print(f"\n🔍 Medium->High切换点分析:")
    print(f"  Medium阈值: {medium_threshold:.2f}m")
    print(f"  当前SSE距离: {sse_distance:.2f}m")
    print(f"  距离切换点: {abs(sse_distance - medium_threshold):.2f}m")

    if abs(sse_distance - medium_threshold) < medium_threshold * 0.1:  # 10%容忍度
        print(f"  🚨 相机在切换边界附近，可能出现不稳定!")

    # 模拟相机移动，找出问题区域
    print(f"\n🧪 相机移动模拟 (寻找死区):")

    # 从当前位置向中心移动
    direction = Gf.Vec3f(
        region_bounds.center[0] - camera_position[0],
        region_bounds.center[1] - camera_position[1],
        region_bounds.center[2] - camera_position[2]
    )
    direction_length = math.sqrt(direction[0]**2 + direction[1]**2 + direction[2]**2)
    if direction_length > 0:
        direction = Gf.Vec3f(direction[0]/direction_length, direction[1]/direction_length, direction[2]/direction_length)

    # 测试不同距离
    test_distances = []
    current_dist = center_distance
    for i in range(10):
        test_distances.append(current_dist * (1.0 - i * 0.1))  # 逐渐靠近

    print(f"  距离     -> LOD选择   SSE距离   状态")
    print(f"  -------- -> --------  --------  --------")

    dead_zones = []

    for test_dist in test_distances:
        if test_dist <= 0:
            continue

        # 计算测试相机位置
        test_camera_pos = Gf.Vec3f(
            region_bounds.center[0] - direction[0] * test_dist,
            region_bounds.center[1] - direction[1] * test_dist,
            region_bounds.center[2] - direction[2] * test_dist
        )

        # 测试LOD选择
        test_selected_lod, _ = scheduler.select_lod_by_sse_and_distance(
            region_bounds, test_camera_pos, verbose=False
        )

        # 计算SSE距离
        test_sse_distance = scheduler.calculate_distance_to_bounding_sphere(
            test_camera_pos, region_bounds.center, region_bounds.size
        )

        # 检查是否有匹配的content
        all_tiles = manager.collect_all_tiles()
        lod_name_to_level = {"High": 20, "Medium": 18, "Low": 16, "VeryLow": 14}
        target_level = lod_name_to_level.get(test_selected_lod, 14)

        has_content = False
        for tile in all_tiles:
            for content in tile.get('content_nodes', []):
                if content.get('lod_level') == target_level:
                    has_content = True
                    break
            if has_content:
                break

        status = "✅有内容" if has_content else "❌死区"
        if not has_content:
            dead_zones.append((test_dist, test_selected_lod, target_level))

        print(f"  {test_dist:6.1f}m -> {test_selected_lod:8s}  {test_sse_distance:6.2f}m  {status}")

    # 总结死区
    if dead_zones:
        print(f"\n🚨 发现 {len(dead_zones)} 个死区:")
        for dist, lod, level in dead_zones:
            print(f"  距离 {dist:.1f}m: 选择{lod} (LOD {level})，但无对应内容")

        print(f"\n💡 解决建议:")
        print(f"  1. 检查tileset数据是否缺少某些LOD级别的内容")
        print(f"  2. 调整LOD距离范围，避免选择不存在的LOD")
        print(f"  3. 添加LOD回退机制：如果目标LOD无内容，选择最接近的可用LOD")
    else:
        print(f"\n✅ 未发现明显的死区，问题可能在其他地方")

def debug_lod_switching_gap():
    """调试LOD切换空白区域问题"""
    print("\n" + "=" * 60)
    print("LOD切换空白区域问题调试")
    print("=" * 60)

    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 错误：无法获取USD Stage")
        return

    # 创建调度器
    try:
        config = get_default_lod_config()
        scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)
        manager = TilesetLODManager(stage, camera_path="/World/Camera", lod_scheduler=scheduler, config=config)
        print("✅ 成功创建调度器")
    except Exception as e:
        print(f"❌ 创建调度器失败: {e}")
        return

    # 获取相机位置和区域边界
    camera_position = scheduler.get_camera_position()
    region_bounds = manager.get_tileset_region_bounds()

    if not camera_position or not region_bounds:
        print("❌ 无法获取相机位置或区域边界")
        return

    print(f"📍 相机位置: {camera_position}")
    print(f"📦 区域中心: {region_bounds.center}")

    # 计算当前距离
    current_distance = math.sqrt(
        (camera_position[0] - region_bounds.center[0])**2 +
        (camera_position[1] - region_bounds.center[1])**2 +
        (camera_position[2] - region_bounds.center[2])**2
    )
    print(f"📏 当前距离: {current_distance:.2f}m")

    # 获取距离范围
    distance_ranges, lod_threshold_distances = scheduler.calculate_lod_distance_ranges()

    print(f"\n📊 LOD距离范围分析:")
    for lod_name in ["High", "Medium", "Low", "VeryLow"]:
        if lod_name in distance_ranges:
            min_dist, max_dist = distance_ranges[lod_name]
            max_dist_str = f"{max_dist:.1f}m" if max_dist != float('inf') else "∞"

            # 检查当前距离是否在范围内
            in_range = min_dist <= current_distance < max_dist
            status = "✅ 当前" if in_range else "⭕"

            print(f"  {lod_name:8s}: {min_dist:6.1f}m - {max_dist_str:>8s} {status}")

    # 选择当前LOD
    selected_lod, lod_info = scheduler.select_lod_by_sse_and_distance(
        region_bounds, camera_position, verbose=False
    )
    print(f"\n🎯 当前选择的LOD: {selected_lod}")

    # 收集所有tiles并检查LOD内容
    all_tiles = manager.collect_all_tiles()
    print(f"\n📋 检查tiles的LOD内容分布:")

    lod_name_to_level = {"High": 20, "Medium": 18, "Low": 16, "VeryLow": 14}
    lod_content_stats = {20: 0, 18: 0, 16: 0, 14: 0}

    for tile in all_tiles:
        content_nodes = tile.get('content_nodes', [])
        for content in content_nodes:
            content_lod_level = content.get('lod_level', 14)
            if content_lod_level in lod_content_stats:
                lod_content_stats[content_lod_level] += 1

    print(f"  LOD 20 (High):   {lod_content_stats[20]} 个content节点")
    print(f"  LOD 18 (Medium): {lod_content_stats[18]} 个content节点")
    print(f"  LOD 16 (Low):    {lod_content_stats[16]} 个content节点")
    print(f"  LOD 14 (VeryLow): {lod_content_stats[14]} 个content节点")

    # 分析问题
    target_lod_level = lod_name_to_level.get(selected_lod, 14)
    available_content = lod_content_stats.get(target_lod_level, 0)

    print(f"\n🔍 问题分析:")
    print(f"  当前应该显示: LOD {target_lod_level} ({selected_lod})")
    print(f"  可用的content节点: {available_content} 个")

    if available_content == 0:
        print(f"  🚨 问题确认: 没有LOD {target_lod_level}的content节点！")
        print(f"  💡 这就是tile消失的原因")

        # 建议解决方案
        print(f"\n💡 解决方案建议:")
        print(f"  1. 检查tileset数据是否包含LOD {target_lod_level}的内容")
        print(f"  2. 调整LOD距离范围，避免选择不存在的LOD级别")
        print(f"  3. 添加LOD级别回退机制")

        # 显示可用的LOD级别
        available_lods = [level for level, count in lod_content_stats.items() if count > 0]
        print(f"  4. 可用的LOD级别: {available_lods}")

    else:
        print(f"  ✅ LOD内容可用，问题可能在其他地方")

    # 测试不同距离下的LOD选择
    print(f"\n🧪 距离测试 (模拟相机移动):")
    test_distances = [current_distance * 2, current_distance * 1.5, current_distance, current_distance * 0.5, current_distance * 0.1]

    for test_dist in test_distances:
        # 创建测试用的边界框
        test_center = region_bounds.center
        test_bbox = BoundingBox(
            Gf.Vec3f(test_center[0] - 10, test_center[1] - 10, test_center[2] - 10),
            Gf.Vec3f(test_center[0] + 10, test_center[1] + 10, test_center[2] + 10)
        )

        # 计算测试相机位置
        direction = Gf.Vec3f(1, 0, 0)  # 简化：假设相机在X轴方向
        test_camera_pos = Gf.Vec3f(
            test_center[0] + direction[0] * test_dist,
            test_center[1] + direction[1] * test_dist,
            test_center[2] + direction[2] * test_dist
        )

        test_selected_lod, _ = scheduler.select_lod_by_sse_and_distance(
            test_bbox, test_camera_pos, verbose=False
        )

        test_target_level = lod_name_to_level.get(test_selected_lod, 14)
        test_available = lod_content_stats.get(test_target_level, 0)
        status = "✅" if test_available > 0 else "❌"

        print(f"  距离 {test_dist:6.1f}m -> {test_selected_lod:8s} (LOD {test_target_level}) {status}")

def test_lod_selection_fix():
    """测试LOD选择修复效果 - 验证远距离LOD选择"""
    print("\n" + "=" * 60)
    print("LOD选择修复效果测试")
    print("=" * 60)

    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 错误：无法获取USD Stage")
        return

    # 创建调度器
    try:
        config = get_default_lod_config()
        scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)
        manager = TilesetLODManager(stage, camera_path="/World/Camera", lod_scheduler=scheduler, config=config)
        print("✅ 成功创建调度器")
    except Exception as e:
        print(f"❌ 创建调度器失败: {e}")
        return

    # 获取区域边界
    region_bounds = manager.get_tileset_region_bounds()
    if not region_bounds:
        print("❌ 无法获取区域边界")
        return

    print(f"📦 区域中心: {region_bounds.center}")
    print(f"📦 区域尺寸: {region_bounds.size}")

    # 测试不同距离下的LOD选择
    print(f"\n🧪 距离测试 - 验证远距离LOD选择:")
    print(f"距离(m)  -> 选择LOD    -> 状态")
    print(f"-------- -> --------   -> --------")

    # 创建测试距离列表：从很远到很近
    test_distances = [2000, 1000, 500, 300, 200, 150, 100, 80, 60, 40, 30, 20, 10, 5, 1]

    center = region_bounds.center

    for test_dist in test_distances:
        # 创建测试相机位置（在X轴方向）
        test_camera_pos = Gf.Vec3f(
            center[0] + test_dist,
            center[1],
            center[2]
        )

        # 测试LOD选择
        try:
            selected_lod, lod_info = scheduler.select_lod_by_sse_and_distance(
                region_bounds, test_camera_pos, verbose=False
            )

            # 检查是否有对应的content
            all_tiles = manager.collect_all_tiles()
            lod_name_to_level = {"High": 20, "Medium": 18, "Low": 16, "VeryLow": 14}
            target_level = lod_name_to_level.get(selected_lod, 14)

            content_count = 0
            for tile in all_tiles:
                for content in tile.get('content_nodes', []):
                    if content.get('lod_level') == target_level:
                        content_count += 1

            status = f"✅{content_count}个内容" if content_count > 0 else "❌无内容"

            print(f"{test_dist:6.0f}m  -> {selected_lod:8s}   -> {status}")

        except Exception as e:
            print(f"{test_dist:6.0f}m  -> 错误       -> {str(e)[:20]}...")

    # 获取距离范围信息
    distance_ranges, lod_threshold_distances = scheduler.calculate_lod_distance_ranges()

    print(f"\n📊 当前LOD距离范围:")
    for lod_name in ["High", "Medium", "Low", "VeryLow"]:
        if lod_name in distance_ranges:
            min_dist, max_dist = distance_ranges[lod_name]
            max_dist_str = f"{max_dist:.0f}m" if max_dist != float('inf') else "∞"
            threshold = lod_threshold_distances.get(lod_name, 0)
            print(f"  {lod_name:8s}: {min_dist:6.0f}m - {max_dist_str:>6s} (阈值: {threshold:.0f}m)")

    # 检查内容分布
    print(f"\n📋 LOD内容分布:")
    all_tiles = manager.collect_all_tiles()
    lod_content_stats = {20: 0, 18: 0, 16: 0, 14: 0}

    for tile in all_tiles:
        content_nodes = tile.get('content_nodes', [])
        for content in content_nodes:
            content_lod_level = content.get('lod_level', 14)
            if content_lod_level in lod_content_stats:
                lod_content_stats[content_lod_level] += 1

    for level, count in lod_content_stats.items():
        lod_name = {20: "High", 18: "Medium", 16: "Low", 14: "VeryLow"}[level]
        print(f"  LOD {level} ({lod_name:8s}): {count:3d} 个content节点")

    print(f"\n💡 分析结果:")

    # 检查是否有远距离的LOD内容
    has_low_lod = lod_content_stats[16] > 0 or lod_content_stats[14] > 0
    if not has_low_lod:
        print(f"  ⚠️  缺少低LOD内容 (LOD 16/14)，远距离时可能无内容显示")
        print(f"  💡 建议：检查tileset数据是否包含低LOD级别")
    else:
        print(f"  ✅ 有低LOD内容，远距离显示应该正常")

    # 检查距离范围是否合理
    very_low_range = distance_ranges.get("VeryLow", (0, 0))
    if very_low_range[0] > 1000:
        print(f"  ⚠️  VeryLow LOD起始距离过大 ({very_low_range[0]:.0f}m)")
        print(f"  💡 建议：调整SSE阈值或几何误差配置")
    else:
        print(f"  ✅ 距离范围设置合理")

if __name__ == "__main__":
    print("请在Isaac Sim的Script Editor中运行以下函数：")
    print("1. debug_current_lod_state() - 调试当前LOD状态")
    print("2. debug_tileset_lod_visibility() - 专门调试update_tileset_lod_visibility方法")
    print("3. debug_lod_transition_deadzone() - 调试LOD过渡死区问题")
    print("4. test_lod_selection_fix() - 测试LOD选择修复效果 (推荐)")
    print("5. debug_lod_switching_gap() - 调试LOD切换空白区域问题")
    print("6. test_distance_calculation() - 测试距离计算")
