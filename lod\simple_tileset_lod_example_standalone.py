"""
简单Tileset LOD示例 - Standalone版本（重构版）
使用TilesetExampleTemplate简化示例脚本，主要负责配置示例信息
"""

import time
import sys
import os

# Isaac Sim standalone 支持
from isaacsim import SimulationApp
# HUD 配置标志位
DISP_FPS        = 1 << 0
DISP_RESOLUTION = 1 << 3
DISP_DEV_MEM    = 1 << 13
DISP_HOST_MEM   = 1 << 14

config = {
    "width": 1280,
    "height": 720,
    "headless": False,
    "display_options": DISP_FPS | DISP_RESOLUTION | DISP_DEV_MEM | DISP_HOST_MEM,
}

simulation_app = SimulationApp(launch_config=config)

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from pxr import Gf

# 导入重构后的模块
try:
    from tileset_example_template import TilesetExampleTemplate, TilesetConfig
    print("✓ Successfully imported tileset_example_template")
except ImportError as e:
    print(f"⚠️ Warning: Could not import tileset_example_template: {e}")
    print("Please ensure the refactored modules are available")
    sys.exit(1)

# 创建自定义配置
def create_example_config():
    """创建示例配置"""
    config = TilesetConfig()

    # 自定义路径配置 - 用户需要修改这些路径
    config.tileset_path = "E:/wanleqi/isaacsim-python-scripts/lod/tileset_data/florenz_village/hierarchy_all_18/tileset.json"
    config.usd_file_path = "E:/wanleqi/LOD_Demo/lod-demo-tileset-all-18.usd"

    # 自定义相机移动配置
    config.camera_start_position = Gf.Vec3f(0, 0, 50)
    config.camera_target_position = Gf.Vec3f(20, 0, 100)
    config.camera_movement_duration = 30.0
    config.camera_movement_loop = True

    # 自定义分批显示配置
    config.enable_batch_display = True
    config.batch_interval = 0.15

    # 自定义更新间隔
    config.timer_interval = 1.0
    config.debug_info = True

    return config

def main():
    """主函数 - 重构版Tileset Standalone示例"""
    print("Simple Tileset LOD Example - Standalone Version (Refactored)")
    print("=" * 70)
    print("This version uses the refactored TilesetExampleTemplate for cleaner code.")
    print("The system will start automatic LOD switching immediately.")
    print("=" * 70)

    try:
        # 创建自定义配置
        config = create_example_config()

        # 创建示例模板
        example = TilesetExampleTemplate(config)

        # 运行完整的示例流程
        print("\n🚀 Starting tileset LOD switching...")
        success = example.run_complete_example()

        if success:
            print("\n" + "=" * 50)
            print("✅ Tileset LOD switching started successfully!")
            print("\nFeatures:")
            print("- Uses refactored TilesetLODManager for cleaner architecture")
            print("- Separated concerns: configuration, LOD logic, and utilities")
            print("- SSE (Screen Space Error) based LOD selection")
            print("- Automatic LOD switching based on camera distance")
            print("- Real-time updates as you move the camera")
            print("- Frustum culling and batch display optimization")
            print("\nThe system is now running automatically!")
            print("Move the camera in Omniverse to see LOD changes.")
            print("=" * 50)

            # 显示状态信息
            status = example.get_status()
            print(f"\nSystem Status:")
            print(f"  Stage loaded: {status['stage_loaded']}")
            print(f"  LOD scheduler ready: {status['lod_scheduler_ready']}")
            print(f"  Tileset manager ready: {status['tileset_manager_ready']}")
            print(f"  Auto update active: {status.get('auto_update_active', False)}")

            # 保持脚本运行
            print("\nPress Ctrl+C to stop...")
            try:
                print("Starting main loop...")
                while True:
                    simulation_app.update()
                    time.sleep(0.01)
            except KeyboardInterrupt:
                print("\nStopping...")
                example.stop_automatic_lod_switching()
        else:
            print("❌ Failed to start tileset LOD switching")
            print("💡 Please check your configuration paths and ensure USD scene is available!")

    except Exception as e:
        print(f"❌ Error starting tileset LOD switching: {e}")
        import traceback
        traceback.print_exc()

# 便捷函数
def start_tileset_lod():
    """便捷函数：启动tileset LOD切换"""
    config = create_example_config()
    example = TilesetExampleTemplate(config)
    return example.run_complete_example()

def stop_tileset_lod():
    """便捷函数：停止tileset LOD切换"""
    # 注意：这个函数需要访问全局的example实例
    # 在实际使用中，建议保存example实例的引用
    print("To stop LOD switching, call example.stop_automatic_lod_switching() on your example instance")
    return True

# 使用说明
print("\n" + "=" * 70)
print("Simple Tileset LOD Example - Standalone Version (Refactored)")
print("=" * 70)
print("\nThis script uses the refactored TilesetExampleTemplate for cleaner code.")
print("It requires USD scenes created by tileset_usd_creator.py.")
print("\nFeatures:")
print("- Uses refactored TilesetLODManager for cleaner architecture")
print("- Separated concerns: configuration, LOD logic, and utilities")
print("- SSE (Screen Space Error) based LOD selection")
print("- Automatic LOD switching based on camera distance")
print("- Real-time updates as you move the camera")
print("- Frustum culling and batch display optimization")
print("\nQuick Start:")
print("1. First, create USD scene using tileset_usd_creator.py:")
print("   - Run tileset_usd_creator.py in Script Editor")
print("   - This will create lod-demo-tileset.usd with tileset structure")
print("2. Then run this script: python simple_tileset_lod_example_standalone.py")
print("\nAvailable functions:")
print("  - start_tileset_lod()    # Start tileset LOD switching")
print("  - stop_tileset_lod()     # Stop tileset LOD switching")
print("\nOr call main() directly from the console")
print("=" * 70)

# 示例使用
if __name__ == "__main__":
    main()
