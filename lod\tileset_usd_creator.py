"""
Tileset USD Creator - 独立脚本
用于解析tileset.json文件并创建包含USDZ LOD tiles的USD stage
可在<PERSON> Si<PERSON> Script Editor中运行，不需要standalone模式
"""

import json
from math import log
import os
import sys
from pxr import Usd, UsdGeom, Gf, Sdf
import omni.usd
import omni.kit.commands
sys.path.append("E:/wanleqi/isaacsim-python-scripts/lod")

# 添加路径以导入LOD相关模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from lod_scheduler import BoundingBox
    print("✓ Successfully imported lod_scheduler")
except ImportError as e:
    print(f"⚠️ Warning: Could not import lod_scheduler: {e}")
    # 定义简单的BoundingBox类作为备用
    class BoundingBox:
        def __init__(self, min_point, max_point):
            self.min_point = min_point
            self.max_point = max_point
        
        @property
        def center(self):
            return Gf.Vec3f(
                (self.min_point[0] + self.max_point[0]) / 2,
                (self.min_point[1] + self.max_point[1]) / 2,
                (self.min_point[2] + self.max_point[2]) / 2
            )
        
        @property
        def size(self):
            return Gf.Vec3f(
                self.max_point[0] - self.min_point[0],
                self.max_point[1] - self.min_point[1],
                self.max_point[2] - self.min_point[2]
            )

class TilesetUSDCreatorConfig:
    """Tileset USD Creator配置类"""
    def __init__(self):
        # Tileset文件配置
        self.tileset_path = "E:/wanleqi/isaacsim-python-scripts/lod/tileset_data/florenz_village/hierarchy_all_18/tileset.json"

        # 输出USD文件路径
        self.output_usd_path = "E:/wanleqi/LOD_Demo/lod-demo-tileset.usd"

        # 相机配置
        self.camera_path = "/World/Camera"

        # 使用中心化LOD配置
        from lod_config import create_lod_config_from_tileset
        self.lod_config = create_lod_config_from_tileset(self.tileset_path)

def transform_point(p, transform_matrix):
    """
    变换点的函数，应用坐标系修正
    """
    point4 = Gf.Vec4d(p[0], p[1], p[2], 1.0)
    # 使用正确的Transform方法 - 使用矩阵乘法
    transformed = transform_matrix * point4
    return Gf.Vec3f(transformed[0], transformed[1], transformed[2])

def apply_coordinate_system_correction(minB, maxB, debug_info=False):
    """
    应用坐标系修正到边界框
    """
    if debug_info:
        print(f"  Original bounds: {minB} to {maxB}")
    
    # 应用 (90, 180, 0) 旋转的逆变换到坐标点
    # 这相当于将 NUREC 坐标系转换为 USD 坐标系
    
    # 创建旋转矩阵：先绕 Z 轴旋转 180°，再绕 X 轴旋转 90°
    # 注意：我们需要应用逆变换，所以是 (90, -180, 0) 的变换
    rot_z_180 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(0, 0, 1), -180))
    rot_x_90 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(1, 0, 0), 90))
    transform_matrix = rot_z_180 * rot_x_90

    # 重新计算变换后的最小最大值
    all_transformed_points = [
        transform_point(Gf.Vec3f(minB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], maxB[2]), transform_matrix)
    ]

    # 计算变换后的实际边界
    min_x = min(p[0] for p in all_transformed_points)
    max_x = max(p[0] for p in all_transformed_points)
    min_y = min(p[1] for p in all_transformed_points)
    max_y = max(p[1] for p in all_transformed_points)
    min_z = min(p[2] for p in all_transformed_points)
    max_z = max(p[2] for p in all_transformed_points)

    corrected_minB = Gf.Vec3f(min_x, min_y, min_z)
    corrected_maxB = Gf.Vec3f(max_x, max_y, max_z)
    
    if debug_info:
        print(f"  Corrected bounds: {corrected_minB} to {corrected_maxB}")
    
    return corrected_minB, corrected_maxB

def load_tileset_file(tileset_path):
    """加载tileset.json文件"""
    try:
        print(f"Loading tileset file: {tileset_path}")
        
        # 检查文件是否存在
        if not os.path.exists(tileset_path):
            print(f"ERROR: Tileset file not found: {tileset_path}")
            return None
        
        with open(tileset_path, 'r', encoding='utf-8') as f:
            tileset_data = json.load(f)
        
        print(f"Successfully loaded tileset file: {tileset_path}")
        return tileset_data
        
    except Exception as e:
        print(f"ERROR: Failed to load tileset file: {e}")
        return None

def parse_tileset_bounding_box(bounding_volume):
    """解析tileset中的边界框"""
    if 'box' in bounding_volume:
        # 3D Tiles格式的box
        box = bounding_volume['box']
        if len(box) >= 12:
            center = Gf.Vec3f(box[0], box[1], box[2])
            
            # 计算半轴长度
            x_axis = Gf.Vec3f(box[3], box[4], box[5])
            y_axis = Gf.Vec3f(box[6], box[7], box[8])
            z_axis = Gf.Vec3f(box[9], box[10], box[11])
            
            # 计算边界框的最小和最大点
            x_extent = abs(x_axis[0]) + abs(y_axis[0]) + abs(z_axis[0])
            y_extent = abs(x_axis[1]) + abs(y_axis[1]) + abs(z_axis[1])
            z_extent = abs(x_axis[2]) + abs(y_axis[2]) + abs(z_axis[2])
            
            min_point = Gf.Vec3f(
                center[0] - x_extent,
                center[1] - y_extent,
                center[2] - z_extent
            )
            max_point = Gf.Vec3f(
                center[0] + x_extent,
                center[1] + y_extent,
                center[2] + z_extent
            )

            # 应用坐标系修正
            corrected_min_point, corrected_max_point = apply_coordinate_system_correction(min_point, max_point, debug_info=True)
            print(f"  Original bounds: {min_point} to {max_point}")
            print(f"  Corrected bounds: {corrected_min_point} to {corrected_max_point}")    
            
            return BoundingBox(corrected_min_point, corrected_max_point)
    
    elif 'sphere' in bounding_volume:
        # 球形边界
        sphere = bounding_volume['sphere']
        if len(sphere) >= 4:
            center = Gf.Vec3f(sphere[0], sphere[1], sphere[2])
            radius = sphere[3]
            min_point = Gf.Vec3f(center[0] - radius, center[1] - radius, center[2] - radius)
            max_point = Gf.Vec3f(center[0] + radius, center[1] + radius, center[2] + radius)
            return BoundingBox(min_point, max_point)
    
    return None

def load_usdz_file_into_stage(usdz_path, target_path):
    """将USDZ文件加载到stage中的指定路径"""
    try:
        stage = omni.usd.get_context().get_stage()
        
        # 检查文件是否存在
        if not os.path.exists(usdz_path):
            print(f"Warning: USDZ file not found: {usdz_path}")
            return False
        
        # 使用USD的SdfLayer来引用USDZ文件
        layer = Sdf.Layer.FindOrOpen(usdz_path)
        if layer:
            # 创建引用
            prim = stage.DefinePrim(target_path)
            prim.GetReferences().AddReference(usdz_path)
            print(f"Successfully referenced USDZ file: {usdz_path} at {target_path}")
            return True
        else:
            print(f"Warning: Could not open USDZ file: {usdz_path}")
            return False
    except Exception as e:
        print(f"Error loading USDZ file {usdz_path}: {e}")
        return False

def setup_physics_scene(stage):
    """设置基本的物理场景以避免物理系统错误"""
    try:
        from pxr import UsdPhysics

        # 检查是否已有物理场景
        physics_scene_path = "/World/PhysicsScene"
        physics_scene_prim = stage.GetPrimAtPath(physics_scene_path)

        if not physics_scene_prim or not physics_scene_prim.IsValid():
            print("Creating physics scene...")
            # 创建物理场景
            physics_scene_prim = stage.DefinePrim(physics_scene_path, "PhysicsScene")
            physics_scene = UsdPhysics.Scene(physics_scene_prim)

            # 设置基本的物理参数
            physics_scene.CreateGravityDirectionAttr().Set(Gf.Vec3f(0.0, 0.0, -1.0))
            physics_scene.CreateGravityMagnitudeAttr().Set(9.81)

            print("Physics scene created successfully")
        else:
            print("Physics scene already exists")

        return True

    except Exception as e:
        print(f"Warning: Failed to setup physics scene: {e}")
        print("Continuing without physics scene...")
        return False

def create_tileset_prims_hierarchy(stage, tile_data, parent_path, base_path, created_prims, depth=0, tile_index=0, global_tile_counter=None, config=None):
    """递归创建tileset层次结构的prims - 保持3D Tiles的树状结构"""
    try:
        # 如果没有提供全局计数器，创建一个
        if global_tile_counter is None:
            global_tile_counter = [0]  # 使用列表以便在递归中修改

        # 使用传入的tile_index作为当前层级内的索引
        # 这样同一depth层级内的tiles会有连续的index
        current_tile_index = tile_index

        print(f"Processing tile at depth {depth}, depth_index {current_tile_index}")
        print(f"Tile data keys: {list(tile_data.keys())}")

        # 获取几何误差
        geometric_error = tile_data.get('geometricError', 1.0)
        print(f"Geometric error: {geometric_error}")

        # 获取内容URI
        content = tile_data.get('content', {})
        print(f"Content: {content}")
        uri = content.get('uri', '')

        # 检查是否有内容URI
        has_content = bool(uri)
        print(f"Has content URI: {has_content}")

        # 为每个 tile 创建一个容器节点，保持层次结构
        tile_name = f"Tile_{depth}_{current_tile_index}"
        current_tile_path = f"{parent_path}/{tile_name}"
        print(f"Creating tile container: {current_tile_path}")

        # 创建 tile 容器
        tile_prim = stage.DefinePrim(current_tile_path)
        tile_prim.SetTypeName("Xform")

        # 设置 tile 的基本属性
        tile_prim.CreateAttribute("tileset:depth", Sdf.ValueTypeNames.Int).Set(depth)
        tile_prim.CreateAttribute("tileset:tileIndex", Sdf.ValueTypeNames.Int).Set(current_tile_index)
        tile_prim.CreateAttribute("tileset:geometricError", Sdf.ValueTypeNames.Float).Set(geometric_error)
        tile_prim.CreateAttribute("tileset:hasContent", Sdf.ValueTypeNames.Bool).Set(has_content)

        # 解析并设置边界框
        bounding_box = parse_tileset_bounding_box(tile_data.get('boundingVolume', {}))
        if bounding_box:
            tile_prim.CreateAttribute("tileset:minBounds", Sdf.ValueTypeNames.Float3).Set(bounding_box.min_point)
            tile_prim.CreateAttribute("tileset:maxBounds", Sdf.ValueTypeNames.Float3).Set(bounding_box.max_point)

        # 如果有内容，创建内容节点
        if has_content:
            # 根据几何误差确定LOD级别
            # if config and hasattr(config, 'lod_config'):
            #     lod_level = config.lod_config.get_lod_level_numeric(geometric_error)
            # else:
                # 回退到默认计算方法
            lod_level = int(20 - log(geometric_error, 2))

            # 构建完整的文件路径
            if os.path.isabs(uri):
                usdz_path = uri
            else:
                usdz_path = os.path.join(base_path, uri).replace('\\', '/')

            print(f"Creating content for URI: {uri}")
            print(f"USDZ path: {usdz_path}")
            print(f"LOD level: {lod_level}")

            # 在 tile 容器内创建内容节点
            content_path = f"{current_tile_path}/Content_LOD_{lod_level}"
            content_prim = stage.DefinePrim(content_path)
            content_prim.SetTypeName("Xform")

            # 设置内容属性
            content_prim.CreateAttribute("tileset:lodLevel", Sdf.ValueTypeNames.Int).Set(lod_level)
            content_prim.CreateAttribute("tileset:uri", Sdf.ValueTypeNames.String).Set(uri)
            content_prim.CreateAttribute("tileset:geometricError", Sdf.ValueTypeNames.Float).Set(geometric_error)

            # 加载USDZ文件
            usdz_loaded = load_usdz_file_into_stage(usdz_path, f"{content_path}/USDZContent")

            if usdz_loaded:
                # 设置初始可见性（根据LOD级别决定）
                imageable = UsdGeom.Imageable(content_prim)
                vis_attr = imageable.GetVisibilityAttr()
                # 默认只显示最高级别的LOD（最小的geometric error）
                visible = (depth <= 1)  # 只显示前几层
                # vis_attr.Set(UsdGeom.Tokens.inherited if visible else UsdGeom.Tokens.invisible)

                created_prims.append({
                    'tile_path': current_tile_path,
                    'content_path': content_path,
                    'lod_level': lod_level,
                    'geometric_error': geometric_error,
                    'uri': uri,
                    'usdz_path': usdz_path,
                    'depth': depth,
                    'tile_index': current_tile_index,  # 这是同一depth层级内的索引
                    'visible': visible
                })

                print(f"✓ Created tile with content at {content_path}")
                print(f"  Depth: {depth}, LOD Level: {lod_level}, GeometricError: {geometric_error}")
                print(f"  URI: {uri}, Visible: {visible}")
            else:
                print(f"✗ Failed to load USDZ for tile at {current_tile_path}")
        else:
            print(f"Container tile (no content) at {current_tile_path}")

        # 递归处理子瓦片 - 关键：使用当前tile作为父路径
        children = tile_data.get('children', [])
        print(f"Processing {len(children)} children for tile at depth {depth}")
        for child_index, child_tile in enumerate(children):
            create_tileset_prims_hierarchy(stage, child_tile, current_tile_path, base_path, created_prims, depth + 1, child_index, global_tile_counter, config)

    except Exception as e:
        print(f"Error creating tileset prim at depth {depth}: {e}")
        import traceback
        traceback.print_exc()

def create_stage_and_load_tileset_prims(tileset_data, config: 'TilesetUSDCreatorConfig' = None):
    """创建stage并根据tileset数据加载USDZ prims"""
    print("Creating stage and loading tileset USDZ prims...")

    # 获取或创建stage
    context = omni.usd.get_context()

    # 首先尝试获取现有的stage
    stage = context.get_stage()

    if stage:
        print("Using existing stage")
        # 清理现有stage的内容（可选）
        # stage.RemovePrim("/World")
    else:
        print("Creating new stage...")
        # 创建新的stage文件
        try:
            # 使用omni.usd创建新stage
            success = context.new_stage()
            if success:
                stage = context.get_stage()
                print(f"Successfully created new stage")
            else:
                print("Failed to create new stage, creating in-memory stage")
                stage = Usd.Stage.CreateNew()
        except Exception as e:
            print(f"Error creating stage: {e}, creating in-memory stage")
            stage = Usd.Stage.CreateNew()

    if not stage:
        print("ERROR: Failed to create or get stage")
        return None, None

    # 设置物理场景以避免物理系统错误
    # setup_physics_scene(stage)

    # 获取tileset的基础路径
    base_path = os.path.dirname(config.tileset_path) if config else os.path.dirname(tileset_data.get('uri', ''))

    # 解析tileset层次结构并创建prims
    root_tile = tileset_data.get('root')
    if not root_tile:
        print("ERROR: No root tile found in tileset")
        return None, None

    # 解析根节点的边界框
    root_bounds = parse_tileset_bounding_box(root_tile.get('boundingVolume', {}))
    if not root_bounds:
        print("ERROR: Failed to parse root bounding box")
        return None, None

    print(f"Tileset root bounds: {root_bounds.min_point} to {root_bounds.max_point}")

    # 创建区域容器
    region_path = "/World/TilesetRegion"
    region_prim = stage.DefinePrim(region_path)
    region_prim.SetTypeName("Xform")

    # 递归创建tileset层次结构的prims
    created_prims = []
    global_tile_counter = [0]  # 全局tile计数器
    create_tileset_prims_hierarchy(stage, root_tile, region_path, base_path, created_prims, 0, 0, global_tile_counter, config)

    print(f"Created {len(created_prims)} tileset prims")

    # 创建相机
    camera_path = "/World/Camera"
    camera = UsdGeom.Camera.Define(stage, camera_path)

    # 设置相机位置
    center = root_bounds.center
    region_size = root_bounds.size
    max_dimension = max(region_size[0], region_size[1], region_size[2])
    camera_distance = max_dimension * 0.3  # 设置为区域最大尺寸的30%

    camera_position = Gf.Vec3f(
        center[0],
        center[1],
        center[2] + camera_distance
    )

    xformable = UsdGeom.Xformable(camera)
    existing_ops = xformable.GetOrderedXformOps()
    has_translate = any(op.GetOpType() == UsdGeom.XformOp.TypeTranslate for op in existing_ops)
    
    if not has_translate:
        xformable.AddTranslateOp().Set(camera_position)
    else:
        for op in existing_ops:
            if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                op.Set(camera_position)
                break

    print(f"Created camera at {camera_path}")
    print(f"Camera position: {camera_position}")
    print(f"Distance to region center: {camera_distance:.1f}")

    print("Tileset stage and prims created successfully!")
    print(f"Region bounds: {root_bounds.min_point} to {root_bounds.max_point}")
    print(f"Region size: {root_bounds.size}")
    print(f"Region center: {center}")

    return stage, root_bounds

def save_stage_to_file(stage, output_path):
    """保存stage到USD文件"""
    try:
        print(f"Saving stage to: {output_path}")

        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"Created output directory: {output_dir}")

        # 保存stage
        stage.GetRootLayer().Export(output_path)
        print(f"Successfully saved stage to: {output_path}")
        return True

    except Exception as e:
        print(f"ERROR: Failed to save stage: {e}")
        return False

def create_tileset_usd_scene(config: TilesetUSDCreatorConfig = None):
    """创建tileset USD场景的主函数"""
    print("=== Tileset USD Creator ===")
    print("Creating USD scene from tileset.json...")

    if not config:
        config = TilesetUSDCreatorConfig()

    # 1. 检查tileset文件路径
    if not config.tileset_path or not os.path.exists(config.tileset_path):
        print(f"ERROR: Tileset file not found: {config.tileset_path}")
        return None

    # 2. 加载tileset文件
    tileset_data = load_tileset_file(config.tileset_path)
    if not tileset_data:
        return None

    # 3. 创建stage并加载tileset prims
    stage, region_bounds = create_stage_and_load_tileset_prims(tileset_data, config)
    if not stage or not region_bounds:
        return None

    # 4. 保存stage到文件
    # if config.output_usd_path:
    #     success = save_stage_to_file(stage, config.output_usd_path)
    #     if success:
    #         print(f"\n✅ Tileset USD scene created successfully!")
    #         print(f"Output file: {config.output_usd_path}")
    #         print(f"Tileset file: {config.tileset_path}")
    #         print(f"Region bounds: {region_bounds.min_point} to {region_bounds.max_point}")
    #         print(f"Region center: {region_bounds.center}")
    #         return stage, region_bounds, config.output_usd_path
    #     else:
    #         print("❌ Failed to save USD file")
    #         return None
    # else:
    print(f"\n✅ Tileset USD scene created in memory!")
    print(f"Tileset file: {config.tileset_path}")
    print(f"Region bounds: {region_bounds.min_point} to {region_bounds.max_point}")
    print(f"Region center: {region_bounds.center}")
    return stage, region_bounds, None

def main():
    """主函数 - 可在Script Editor中运行"""
    print("Tileset USD Creator - Script Editor Version")
    print("=" * 60)
    print("This script creates a USD scene from tileset.json structure.")
    print("It loads USDZ files and sets up LOD hierarchy for dynamic loading.")
    print("=" * 60)

    try:
        # 创建配置
        config = TilesetUSDCreatorConfig()

        # 可以在这里修改配置
        # config.tileset_path = "path/to/your/tileset.json"
        # config.output_usd_path = "path/to/your/output.usd"

        # 创建tileset USD场景
        result = create_tileset_usd_scene(config)

        if result:
            stage, region_bounds, output_path = result
            print("\n" + "=" * 50)
            print("✅ Tileset USD scene created successfully!")
            print("\nFeatures:")
            print("- Tileset.json structure parsed")
            print("- USDZ files loaded with proper LOD hierarchy")
            print("- Bounding boxes and geometric errors set")
            print("- Camera positioned automatically")
            print("- Physics scene configured")
            if output_path:
                print(f"- USD file saved to: {output_path}")
            print("\nThe scene is ready for use with simple_tileset_lod_example_standalone.py")
            print("=" * 50)
        else:
            print("❌ Failed to create tileset USD scene")

    except Exception as e:
        print(f"❌ Error creating tileset USD scene: {e}")
        import traceback
        traceback.print_exc()

# 便捷函数
def create_tileset_scene():
    """便捷函数：创建tileset场景"""
    config = TilesetUSDCreatorConfig()
    return create_tileset_usd_scene(config)

def create_tileset_scene_with_custom_paths(tileset_path, output_path):
    """便捷函数：使用自定义路径创建tileset场景"""
    config = TilesetUSDCreatorConfig()
    config.tileset_path = tileset_path
    config.output_usd_path = output_path
    return create_tileset_usd_scene(config)

# 使用说明
print("\n" + "=" * 60)
print("Tileset USD Creator - Script Editor Version")
print("=" * 60)
print("\nThis script creates USD scenes from tileset.json files.")
print("It can be run directly in Isaac Sim Script Editor.")
print("\nFeatures:")
print("- Parses tileset.json structure")
print("- Loads USDZ files with proper LOD hierarchy")
print("- Sets up bounding boxes and geometric errors")
print("- Creates camera and physics scene")
print("- Saves USD file for use with LOD system")
print("\nQuick Start:")
print("1. Ensure tileset_simple.json exists in tileset_data/florenz_village/")
print("2. Ensure USDZ files (16.usdz, 18.usdz, 20.usdz) exist in the same directory")
print("3. Run: main() or create_tileset_scene()")
print("\nAvailable functions:")
print("  - main()                           # Run with default config")
print("  - create_tileset_scene()           # Create scene with default paths")
print("  - create_tileset_scene_with_custom_paths(tileset_path, output_path)")
print("=" * 60)

# 示例使用
if __name__ == "__main__":
    main()
