#!/usr/bin/env python3
"""
测试每个Tile独立LOD选择的脚本
验证修复后的LOD调度器是否能根据每个Tile的距离独立选择LOD级别
"""

import omni.usd
from pxr import Gf
import math
from lod.lod_scheduler import TilesetLODManager, LODScheduler
from lod.lod_config import create_lod_config_from_tileset

def test_per_tile_lod_selection():
    """测试每个Tile独立LOD选择功能"""
    
    print("=" * 80)
    print("测试每个Tile独立LOD选择功能")
    print("=" * 80)
    
    # 获取当前stage
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 错误：无法获取USD stage")
        return False
    
    # 检查相机
    camera_path = "/World/Camera"
    camera = stage.GetPrimAtPath(camera_path)
    if not camera:
        print(f"❌ 错误：相机未找到 {camera_path}")
        return False
    
    try:
        # 创建LOD配置（假设有tileset.json文件）
        tileset_path = "path/to/your/tileset.json"  # 请根据实际情况修改
        try:
            lod_config = create_lod_config_from_tileset(tileset_path)
        except:
            # 如果没有tileset文件，使用默认配置
            from lod.lod_config import get_default_lod_config
            lod_config = get_default_lod_config()
            print("⚠️  使用默认LOD配置（未找到tileset.json）")
        
        # 创建LOD调度器
        lod_scheduler = LODScheduler(stage, camera_path, centralized_config=lod_config)
        
        # 创建Tileset LOD管理器
        tileset_manager = TilesetLODManager(
            stage=stage,
            camera_path=camera_path,
            lod_scheduler=lod_scheduler,
            config=lod_config
        )
        
        print("✅ LOD系统初始化成功")
        
        # 获取相机位置
        from pxr import UsdGeom
        xformable = UsdGeom.Xformable(camera)
        transform = xformable.GetLocalTransformation()
        camera_position = Gf.Vec3f(transform.ExtractTranslation())
        
        print(f"📍 相机位置: {camera_position}")
        
        # 测试1：执行LOD更新并观察结果
        print("\n" + "=" * 60)
        print("测试1：执行每个Tile独立LOD选择")
        print("=" * 60)
        
        selected_lod, distance, sse = tileset_manager.update_tileset_lod_visibility(verbose=True)
        
        if selected_lod:
            print(f"\n✅ LOD更新成功")
            print(f"   主要LOD级别: {selected_lod}")
            print(f"   相机距离: {distance:.1f}m")
            print(f"   平均距离: {sse:.1f}m")
        else:
            print("❌ LOD更新失败")
            return False
        
        # 测试2：移动相机并再次测试
        print("\n" + "=" * 60)
        print("测试2：模拟相机移动后的LOD选择")
        print("=" * 60)
        
        # 模拟移动相机（这里只是演示，实际中相机会通过UI移动）
        print("💡 提示：请在Isaac Sim中移动相机，然后再次运行LOD更新")
        print("   观察不同距离的Tiles是否选择了不同的LOD级别")
        
        # 再次执行LOD更新
        selected_lod2, distance2, sse2 = tileset_manager.update_tileset_lod_visibility(verbose=True)
        
        # 测试3：分析LOD分布
        print("\n" + "=" * 60)
        print("测试3：分析LOD分布合理性")
        print("=" * 60)
        
        # 收集所有tiles信息进行分析
        all_tiles = tileset_manager.collect_all_tiles()
        if all_tiles:
            print(f"📊 场景中共有 {len(all_tiles)} 个tiles")
            
            # 分析每个tile的距离分布
            tile_distances = []
            for tile in all_tiles:
                if tile.get('bounds'):
                    bounds = tile['bounds']
                    tile_distance = math.sqrt(
                        (camera_position[0] - bounds.center[0])**2 +
                        (camera_position[1] - bounds.center[1])**2 +
                        (camera_position[2] - bounds.center[2])**2
                    )
                    tile_distances.append(tile_distance)
            
            if tile_distances:
                min_dist = min(tile_distances)
                max_dist = max(tile_distances)
                avg_dist = sum(tile_distances) / len(tile_distances)
                
                print(f"📏 Tile距离分布:")
                print(f"   最近距离: {min_dist:.1f}m")
                print(f"   最远距离: {max_dist:.1f}m")
                print(f"   平均距离: {avg_dist:.1f}m")
                print(f"   距离范围: {max_dist - min_dist:.1f}m")
                
                # 判断是否应该有不同的LOD级别
                distance_range = max_dist - min_dist
                if distance_range > 50:  # 如果距离差异超过50米
                    print("✅ 距离差异较大，应该能看到不同的LOD级别")
                else:
                    print("⚠️  距离差异较小，可能大部分Tiles使用相同LOD级别")
        
        print("\n" + "=" * 80)
        print("测试完成！")
        print("=" * 80)
        print("🔍 观察要点：")
        print("1. 查看日志中的'Per-Tile LOD Selection Results'部分")
        print("2. 注意不同tile_index的Tiles是否选择了不同的LOD级别")
        print("3. 距离近的Tiles应该选择High LOD，距离远的选择Low LOD")
        print("4. 'LOD Distribution'应该显示多种LOD级别的分布")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_issue_and_fix():
    """演示问题现象和修复效果"""
    
    print("\n" + "=" * 80)
    print("问题现象演示和修复效果对比")
    print("=" * 80)
    
    print("🔍 原始问题:")
    print("   - 同一depth层级的所有Tiles使用相同的LOD级别")
    print("   - 近处和远处的Tiles被强制使用相同的LOD")
    print("   - LOD选择基于整个区域中心，而不是每个Tile的实际位置")
    
    print("\n✅ 修复后的行为:")
    print("   - 每个Tile根据自己的包围盒与相机距离独立选择LOD")
    print("   - 近处的Tiles可以是High LOD，远处的是Low LOD")
    print("   - 同一depth层级的不同Tiles可以有不同的LOD级别")
    
    print("\n🔧 关键修改:")
    print("   1. 为每个Tile独立调用select_lod_by_sse_and_distance()")
    print("   2. 使用Tile自己的bounds而不是region_bounds")
    print("   3. 添加LOD分布统计和详细日志")

if __name__ == "__main__":
    print("🚀 启动每个Tile独立LOD选择测试")
    
    # 演示问题和修复
    demonstrate_issue_and_fix()
    
    # 执行测试
    success = test_per_tile_lod_selection()
    
    if success:
        print("\n🎉 测试执行成功！请查看上述日志验证修复效果。")
    else:
        print("\n❌ 测试执行失败，请检查错误信息。")
