#!/usr/bin/env python3
"""
测试智能LOD一致性策略
验证修改后的代码是否解决了LOD混合显示问题，同时保持性能
"""

import omni
from omni import usd
import time
from pxr import Gf, UsdGeom

# 导入LOD调度器
from lod.lod_scheduler import HierarchicalTilesetLODManager
from lod.lod_config import LODConfig

class SmartLODConsistencyTest:
    def __init__(self):
        self.stage = usd.get_context().get_stage()
        self.camera_path = "/World/Camera"
        
        # 创建LOD配置
        self.lod_config = LODConfig()
        
        # 创建LOD管理器
        self.lod_manager = HierarchicalTilesetLODManager(
            stage=self.stage,
            camera_path=self.camera_path,
            config=self.lod_config
        )
        
        # 测试位置 - 特别设计来触发混合LOD情况
        self.test_positions = [
            # 边界位置1 - 可能触发LOD 18和20混合
            Gf.Vec3f(200, 200, 30),
            # 边界位置2 - 另一个可能的混合区域
            Gf.Vec3f(150, 150, 25),
            # 边界位置3 - 中等距离边界
            Gf.Vec3f(300, 300, 40),
            # 远距离 - 应该统一选择Low LOD
            Gf.Vec3f(800, 800, 80),
            # 近距离 - 应该统一选择High LOD
            Gf.Vec3f(80, 80, 15)
        ]
        
    def set_camera_position(self, position: Gf.Vec3f):
        """设置相机位置"""
        camera_prim = self.stage.GetPrimAtPath(self.camera_path)
        if camera_prim:
            xformable = UsdGeom.Xformable(camera_prim)
            # 创建变换矩阵
            transform = Gf.Matrix4d()
            transform.SetTranslate(Gf.Vec3d(position))
            xformable.SetLocalTransformation(transform)
            print(f"📍 Camera moved to: ({position[0]:.1f}, {position[1]:.1f}, {position[2]:.1f})")
        else:
            print("❌ Camera not found!")
            
    def analyze_lod_consistency(self):
        """分析LOD一致性"""
        print(f"\n🔍 Analyzing LOD Consistency...")
        
        # 收集所有可见的内容节点及其LOD级别
        visible_lods = {}  # {lod_level: count}
        visible_contents = []
        
        all_tiles = self.lod_manager.collect_all_tiles()
        
        for tile in all_tiles:
            for content in tile.get('content_nodes', []):
                imageable = UsdGeom.Imageable(content['prim'])
                visibility = imageable.ComputeVisibility()
                
                if visibility == UsdGeom.Tokens.inherited:
                    lod_level = content.get('lod_level', 14)
                    visible_lods[lod_level] = visible_lods.get(lod_level, 0) + 1
                    visible_contents.append({
                        'name': content['name'],
                        'lod_level': lod_level,
                        'tile_name': tile['name']
                    })
        
        # 分析结果
        active_lod_levels = list(visible_lods.keys())
        is_consistent = len(active_lod_levels) <= 1
        
        print(f"   Visible LOD levels: {sorted(active_lod_levels)}")
        print(f"   LOD distribution: {visible_lods}")
        print(f"   Total visible contents: {sum(visible_lods.values())}")
        
        if is_consistent:
            print("   ✅ CONSISTENT: Only one LOD level is visible")
        else:
            print("   ⚠️  MIXED LOD: Multiple LOD levels are visible")
            print("   📋 Detailed breakdown:")
            for content in visible_contents:
                print(f"      - {content['name']} (LOD {content['lod_level']}) in {content['tile_name']}")
                
        return is_consistent, visible_lods, visible_contents
        
    def run_consistency_test(self):
        """运行一致性测试"""
        print("🚀 Starting Smart LOD Consistency Test")
        print("=" * 70)
        
        results = []
        
        for i, position in enumerate(self.test_positions):
            print(f"\n🎯 Test {i+1}/5: Testing position {position}")
            print("-" * 50)
            
            # 设置相机位置
            self.set_camera_position(position)
            time.sleep(0.5)  # 等待场景更新
            
            # 更新LOD可见性
            selected_lod, distance, sse = self.lod_manager.update_tileset_lod_visibility(verbose=True)
            
            # 分析一致性
            is_consistent, visible_lods, visible_contents = self.analyze_lod_consistency()
            
            # 记录结果
            test_result = {
                'position': position,
                'selected_lod': selected_lod,
                'distance': distance,
                'sse': sse,
                'is_consistent': is_consistent,
                'visible_lods': visible_lods,
                'visible_count': len(visible_contents)
            }
            results.append(test_result)
            
            print(f"\n📊 Test {i+1} Summary:")
            print(f"   Position: ({position[0]:.1f}, {position[1]:.1f}, {position[2]:.1f})")
            print(f"   Selected LOD: {selected_lod}")
            print(f"   Distance: {distance:.1f}m")
            print(f"   Consistency: {'✅ PASS' if is_consistent else '❌ FAIL'}")
            print(f"   Visible Contents: {len(visible_contents)}")
            
            print("\n" + "="*70)
            time.sleep(1)  # 观察间隔
            
        # 总结测试结果
        self.summarize_test_results(results)
        
    def summarize_test_results(self, results):
        """总结测试结果"""
        print(f"\n🎯 Test Results Summary")
        print("=" * 70)
        
        consistent_count = sum(1 for r in results if r['is_consistent'])
        total_tests = len(results)
        
        print(f"📊 Overall Statistics:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Consistent Results: {consistent_count}")
        print(f"   Mixed LOD Results: {total_tests - consistent_count}")
        print(f"   Success Rate: {consistent_count/total_tests*100:.1f}%")
        
        print(f"\n📋 Detailed Results:")
        for i, result in enumerate(results):
            status = "✅ PASS" if result['is_consistent'] else "❌ FAIL"
            print(f"   Test {i+1}: {status} - LOD {result['selected_lod']} - "
                  f"Distance {result['distance']:.1f}m - "
                  f"Visible: {result['visible_count']}")
                  
        # 性能评估
        avg_visible = sum(r['visible_count'] for r in results) / len(results)
        print(f"\n⚡ Performance Metrics:")
        print(f"   Average Visible Contents: {avg_visible:.1f}")
        print(f"   Strategy: Smart Consistency (Per-Tile + Rules)")
        
        if consistent_count == total_tests:
            print(f"\n🎉 SUCCESS: All tests passed! Mixed LOD issue resolved.")
        elif consistent_count >= total_tests * 0.8:
            print(f"\n✅ GOOD: Most tests passed. Minor mixed LOD cases may exist.")
        else:
            print(f"\n⚠️  NEEDS IMPROVEMENT: Mixed LOD issue still exists.")

def main():
    """主函数"""
    test = SmartLODConsistencyTest()
    
    print("智能LOD一致性策略测试")
    print("这个测试将验证新的策略是否解决了LOD混合显示问题")
    print("同时保持了性能优化")
    
    input("按Enter键开始测试...")
    
    test.run_consistency_test()

if __name__ == "__main__":
    main()
