#!/usr/bin/env python3
"""
快速验证LOD修复效果的脚本
专门验证同一depth层级的Tiles是否能根据距离选择不同LOD级别
"""

import omni.usd
from pxr import Gf, UsdGeom
import math

def quick_verify_lod_fix():
    """快速验证LOD修复效果"""
    
    print("🔍 快速验证每个Tile独立LOD选择修复效果")
    print("=" * 60)
    
    # 获取stage和相机
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 无法获取USD stage")
        return
    
    camera_path = "/World/Camera"
    camera = stage.GetPrimAtPath(camera_path)
    if not camera:
        print(f"❌ 相机未找到: {camera_path}")
        return
    
    # 获取相机位置
    xformable = UsdGeom.Xformable(camera)
    transform = xformable.GetLocalTransformation()
    camera_position = Gf.Vec3f(transform.ExtractTranslation())
    print(f"📍 相机位置: {camera_position}")
    
    try:
        # 导入修复后的LOD系统
        from lod.lod_scheduler import TilesetLODManager, LODScheduler
        from lod.lod_config import get_default_lod_config
        
        # 使用默认配置
        lod_config = get_default_lod_config()
        
        # 创建LOD调度器和管理器
        lod_scheduler = LODScheduler(stage, camera_path, centralized_config=lod_config)
        tileset_manager = TilesetLODManager(
            stage=stage,
            camera_path=camera_path,
            lod_scheduler=lod_scheduler,
            config=lod_config
        )
        
        print("✅ LOD系统初始化成功")
        
        # 执行LOD更新
        print("\n🔄 执行LOD更新...")
        selected_lod, distance, avg_distance = tileset_manager.update_tileset_lod_visibility(verbose=True)
        
        if selected_lod:
            print(f"\n✅ LOD更新完成")
            print(f"   主要LOD级别: {selected_lod}")
            print(f"   相机到区域中心距离: {distance:.1f}m")
            print(f"   平均Tile距离: {avg_distance:.1f}m")
        else:
            print("❌ LOD更新失败")
            return
        
        # 验证关键点
        print("\n" + "=" * 60)
        print("🔍 验证关键点:")
        print("=" * 60)
        
        print("1. 查看上面的日志，寻找以下关键信息:")
        print("   ✓ 'Per-Tile LOD Selection Results' - 显示每个Tile的独立LOD选择")
        print("   ✓ 'Selected_LOD' 列 - 不同Tiles应该有不同的LOD级别")
        print("   ✓ 'Distance' 列 - 显示每个Tile到相机的实际距离")
        print("   ✓ 'LOD Distribution' - 应该显示多种LOD级别的分布")
        
        print("\n2. 预期行为:")
        print("   ✓ 距离近的Tiles选择High LOD")
        print("   ✓ 距离远的Tiles选择Low或VeryLow LOD")
        print("   ✓ 同一depth层级的不同Tiles可以有不同LOD级别")
        
        print("\n3. 如果修复成功，您应该看到:")
        print("   ✓ 不同tile_index的Tiles有不同的Selected_LOD值")
        print("   ✓ LOD Distribution显示如: {'High': 2, 'Medium': 3, 'Low': 1}")
        print("   ✓ 而不是所有Tiles都使用同一个LOD级别")
        
        # 收集tiles进行简单分析
        all_tiles = tileset_manager.collect_all_tiles()
        if all_tiles:
            print(f"\n📊 场景统计:")
            print(f"   总Tiles数量: {len(all_tiles)}")
            
            # 计算距离分布
            distances = []
            for tile in all_tiles:
                if tile.get('bounds'):
                    bounds = tile['bounds']
                    dist = math.sqrt(
                        (camera_position[0] - bounds.center[0])**2 +
                        (camera_position[1] - bounds.center[1])**2 +
                        (camera_position[2] - bounds.center[2])**2
                    )
                    distances.append(dist)
            
            if distances:
                min_dist = min(distances)
                max_dist = max(distances)
                range_dist = max_dist - min_dist
                
                print(f"   距离范围: {min_dist:.1f}m - {max_dist:.1f}m (差异: {range_dist:.1f}m)")
                
                if range_dist > 30:
                    print("   ✅ 距离差异足够大，应该能看到不同LOD级别")
                else:
                    print("   ⚠️  距离差异较小，大部分Tiles可能使用相同LOD")
        
        print("\n" + "=" * 60)
        print("🎯 验证结论:")
        print("=" * 60)
        print("如果您在上面的日志中看到:")
        print("✅ 不同Tiles有不同的Selected_LOD值 → 修复成功!")
        print("❌ 所有Tiles都有相同的Selected_LOD值 → 可能需要进一步调试")
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def show_before_after_comparison():
    """显示修复前后的对比"""
    
    print("\n" + "=" * 80)
    print("修复前后对比")
    print("=" * 80)
    
    print("🔴 修复前的问题:")
    print("   代码: selected_lod_name, _ = scheduler.select_lod_by_sse_and_distance(region_bounds, camera_pos)")
    print("   结果: 所有Tiles使用基于region_bounds中心的统一LOD级别")
    print("   现象: 同depth层级的所有Tiles同时切换到相同LOD")
    
    print("\n🟢 修复后的行为:")
    print("   代码: tile_selected_lod, _ = scheduler.select_lod_by_sse_and_distance(tile_bounds, camera_pos)")
    print("   结果: 每个Tile基于自己的bounds独立选择LOD级别")
    print("   现象: 近处Tiles是High LOD，远处Tiles是Low LOD")
    
    print("\n📋 关键改进:")
    print("   1. 使用tile_bounds替代region_bounds")
    print("   2. 为每个Tile独立计算距离和LOD")
    print("   3. 添加详细的LOD分布统计")
    print("   4. 支持同一depth层级内的差异化LOD")

if __name__ == "__main__":
    print("🚀 启动LOD修复效果验证")
    
    # 显示修复对比
    show_before_after_comparison()
    
    # 执行验证
    quick_verify_lod_fix()
    
    print("\n🏁 验证完成！请根据上述日志判断修复效果。")
